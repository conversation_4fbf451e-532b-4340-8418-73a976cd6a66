"""
抖音HTML控制器

专门处理通过HTML方式获取抖音视频数据的业务逻辑，包括：
- 精选页面 (jingxuan) 数据获取
- 移动端分享页面 (mobile) 数据获取
- HTML数据解析和转换
"""

import time
from typing import Any, Dict, List, Optional

from fastapi import HTTPException
from loguru import logger


from models.douyin.models import DouyinAweme

from rpc.douyin.html_handler.client import DouyinHTMLClient
from rpc.douyin.html_handler.config import DouyinHTMLConfig
from rpc.douyin.html_handler.exceptions import (
    AntiCrawlerError,
    NetworkError,
    RateLimitError,
)
from rpc.douyin.html_handler.schemas import (
    JingxuanRequest,
    MobileShareRequest,
    PCVideoRequest,
    UserProfileRequest,
)
from utils.douyin.extract.douyin_data_extractor import DouyinDataExtractor
from utils.douyin.extract.jingxuan_data_extractor import JingxuanDataExtractor
from utils.douyin.extract.jingxuan_exceptions import (
    JingxuanConversionError,
    JingxuanDataError,
    JingxuanExtractionError,
    JingxuanNetworkError,
    JingxuanParsingError,
    JingxuanTimeoutError,
    JingxuanValidationError,
)


class DouyinHTMLController:
    """抖音HTML控制器 - 处理HTML方式的数据获取"""

    def __init__(self, config: Optional[DouyinHTMLConfig] = None):
        """
        初始化HTML控制器

        Args:
            config: HTML客户端配置
        """
        self.config = config or DouyinHTMLConfig()
        self.logger = logger.bind(component="DouyinHTMLController")

    async def fetch_jingxuan_video_data(
        self,
        aweme_id: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        save_to_db: bool = True,
    ) -> Dict:
        """
        通过精选页面获取视频数据

        Args:
            aweme_id: 抖音视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、html_response等字段的结果

        Raises:
            HTTPException: 请求失败时抛出
        """
        request_id = f"jingxuan_{aweme_id}_{int(time.time())}"

        self.logger.info(f"[{request_id}] 开始通过精选页面获取视频数据: {aweme_id}")

        try:
            # 创建精选页面请求
            jingxuan_request = JingxuanRequest(
                aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers, timeout=timeout
            )

            # 使用HTML客户端获取数据
            async with DouyinHTMLClient(self.config) as client:
                html_response = await client.fetch_jingxuan_page(jingxuan_request)

            if not html_response.success:
                self.logger.error(f"[{request_id}] 精选页面请求失败: {html_response.error_message}")
                raise HTTPException(status_code=500, detail=f"精选页面请求失败: {html_response.error_message}")

            # 使用专门的精选数据提取方法
            video_data = await self._extract_jingxuan_data_from_html(html_response.html_content, aweme_id, request_id)

            if not video_data:
                self.logger.error(f"[{request_id}] 从精选页面HTML中提取视频数据失败")
                raise HTTPException(status_code=404, detail="从精选页面HTML中提取视频数据失败")

            # 保存到数据库（如果需要）
            if save_to_db:
                await self._save_video_data_to_db(video_data, source="jingxuan")

            self.logger.info(f"[{request_id}] 精选页面数据获取成功")

            return {
                "success": True,
                "data": video_data,
                "html_response": {
                    "status_code": html_response.status_code,
                    "response_time": html_response.response_time,
                    "retry_count": html_response.retry_count,
                    "request_id": html_response.request_id,
                    "url": html_response.url,
                    "final_url": html_response.final_url,
                },
                "source": "jingxuan",
                "request_id": request_id,
            }

        except HTTPException:
            raise
        except JingxuanParsingError as e:
            self.logger.error(
                f"[{request_id}] 精选页面HTML解析失败: {e}",
                extra={
                    "request_id": request_id,
                    "aweme_id": aweme_id,
                    "error_type": "parsing_error",
                    "parsing_stage": getattr(e, "parsing_stage", "unknown"),
                    "content_length": getattr(e, "content_length", 0),
                },
            )
            raise HTTPException(status_code=422, detail=f"精选页面HTML解析失败: {e.message}")
        except JingxuanDataError as e:
            self.logger.error(
                f"[{request_id}] 精选页面数据格式错误: {e}",
                extra={
                    "request_id": request_id,
                    "aweme_id": aweme_id,
                    "error_type": "data_error",
                    "data_type": getattr(e, "data_type", "unknown"),
                    "validation_field": getattr(e, "validation_field", "unknown"),
                },
            )
            raise HTTPException(status_code=422, detail=f"精选页面数据格式错误: {e.message}")
        except JingxuanConversionError as e:
            self.logger.error(
                f"[{request_id}] 精选页面数据转换失败: {e}",
                extra={
                    "request_id": request_id,
                    "aweme_id": aweme_id,
                    "error_type": "conversion_error",
                    "conversion_stage": getattr(e, "conversion_stage", "unknown"),
                    "missing_fields": getattr(e, "missing_fields", []),
                },
            )
            raise HTTPException(status_code=422, detail=f"精选页面数据转换失败: {e.message}")
        except JingxuanValidationError as e:
            self.logger.error(
                f"[{request_id}] 精选页面数据验证失败: {e}",
                extra={
                    "request_id": request_id,
                    "aweme_id": aweme_id,
                    "error_type": "validation_error",
                    "validation_type": getattr(e, "validation_type", "unknown"),
                    "failed_fields": getattr(e, "failed_fields", []),
                },
            )
            raise HTTPException(status_code=422, detail=f"精选页面数据验证失败: {e.message}")
        except JingxuanTimeoutError as e:
            self.logger.error(
                f"[{request_id}] 精选页面操作超时: {e}",
                extra={
                    "request_id": request_id,
                    "aweme_id": aweme_id,
                    "error_type": "timeout_error",
                    "operation": getattr(e, "operation", "unknown"),
                    "timeout_duration": getattr(e, "timeout_duration", 0),
                },
            )
            raise HTTPException(status_code=408, detail=f"精选页面操作超时: {e.message}")
        except JingxuanNetworkError as e:
            self.logger.error(
                f"[{request_id}] 精选页面网络错误: {e}",
                extra={
                    "request_id": request_id,
                    "aweme_id": aweme_id,
                    "error_type": "network_error",
                    "status_code": getattr(e, "status_code", None),
                    "url": getattr(e, "url", None),
                },
            )
            raise HTTPException(status_code=503, detail=f"精选页面网络错误: {e.message}")
        except JingxuanExtractionError as e:
            self.logger.error(
                f"[{request_id}] 精选页面提取错误: {e}",
                extra={
                    "request_id": request_id,
                    "aweme_id": aweme_id,
                    "error_type": "extraction_error",
                    "error_code": getattr(e, "error_code", "unknown"),
                },
            )
            raise HTTPException(status_code=422, detail=f"精选页面提取错误: {e.message}")
        except AntiCrawlerError as e:
            self.logger.error(f"[{request_id}] 遇到反爬虫保护: {e}")
            raise HTTPException(status_code=403, detail=f"遇到反爬虫保护: {e}")
        except RateLimitError as e:
            self.logger.error(f"[{request_id}] 请求频率限制: {e}")
            raise HTTPException(status_code=429, detail=f"请求频率限制: {e}")
        except NetworkError as e:
            self.logger.error(f"[{request_id}] 网络错误: {e}")
            raise HTTPException(status_code=503, detail=f"网络错误: {e}")
        except Exception as e:
            self.logger.exception(f"[{request_id}] 精选页面数据获取异常: {e}")
            raise HTTPException(status_code=500, detail=f"精选页面数据获取异常: {str(e)}")

    async def fetch_mobile_share_video_data(
        self,
        aweme_id: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        save_to_db: bool = True,
    ) -> Dict:
        """
        通过移动端分享页面获取视频数据

        Args:
            aweme_id: 抖音视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、html_response等字段的结果

        Raises:
            HTTPException: 请求失败时抛出
        """
        request_id = f"mobile_{aweme_id}_{int(time.time())}"
        operation_start_time = time.time()

        # 创建结构化日志上下文
        log_context = {
            "request_id": request_id,
            "aweme_id": aweme_id,
            "operation": "fetch_mobile_share_video_data",
            "use_proxy": use_proxy,
            "timeout": timeout,
            "timestamp": operation_start_time,
        }

        self.logger.info(f"[{request_id}] 开始通过移动端分享页面获取视频数据: {aweme_id}", extra=log_context)

        try:
            # 创建移动端分享页面请求
            mobile_request = MobileShareRequest(
                aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers, timeout=timeout
            )

            # 使用HTML客户端获取数据
            async with DouyinHTMLClient(self.config) as client:
                html_response = await client.fetch_mobile_share_page(mobile_request)

            if not html_response.success:
                error_context = {
                    **log_context,
                    "error_type": "html_request_failed",
                    "error_message": html_response.error_message,
                    "operation_duration": time.time() - operation_start_time,
                }
                self.logger.error(
                    f"[{request_id}] 移动端分享页面请求失败: {html_response.error_message}", extra=error_context
                )
                raise HTTPException(status_code=500, detail=f"移动端分享页面请求失败: {html_response.error_message}")

            # HTML内容诊断
            html_diagnostics = self._diagnose_html_content(html_response.html_content, aweme_id, request_id)
            diagnostic_context = {**log_context, **html_diagnostics}

            self.logger.debug(f"[{request_id}] 移动端分享页面HTML内容诊断完成", extra=diagnostic_context)

            # 解析HTML内容提取视频数据
            video_data = await self._extract_video_data_from_html(
                html_response.html_content, aweme_id, source="mobile_share"
            )

            if not video_data:
                error_context = {
                    **log_context,
                    "error_type": "data_extraction_failed",
                    "html_size": len(html_response.html_content) if html_response.html_content else 0,
                    "operation_duration": time.time() - operation_start_time,
                }
                self.logger.error(f"[{request_id}] 从移动端分享页面HTML中提取视频数据失败", extra=error_context)
                raise HTTPException(status_code=404, detail="从移动端分享页面HTML中提取视频数据失败")



            # 保存到数据库（如果需要）
            if save_to_db:
                save_success = await self._save_video_data_to_db(video_data, source="mobile_share")
                if not save_success:
                    self.logger.warning(
                        f"[{request_id}] 移动端分享页面数据保存到数据库失败",
                        extra={**log_context, "save_to_db_failed": True},
                    )

            operation_duration = time.time() - operation_start_time
            success_context = {
                **log_context,
                "operation_duration": operation_duration,
                "save_to_db": save_to_db,
                "html_response_info": {
                    "status_code": html_response.status_code,
                    "response_time": html_response.response_time,
                    "retry_count": html_response.retry_count,
                },
            }

            self.logger.info(f"[{request_id}] 移动端分享页面数据获取成功", extra=success_context)

            return {
                "success": True,
                "data": video_data,
                "html_response": {
                    "status_code": html_response.status_code,
                    "response_time": html_response.response_time,
                    "retry_count": html_response.retry_count,
                    "request_id": html_response.request_id,
                    "url": html_response.url,
                    "final_url": html_response.final_url,
                },
                "source": "mobile_share",
                "request_id": request_id,
            }

        except HTTPException:
            raise
        except AntiCrawlerError as e:
            error_context = {
                **log_context,
                "error_type": "anti_crawler_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] 移动端分享页面遇到反爬虫保护: {e}", extra=error_context)
            # 提供恢复建议
            recovery_suggestion = self._get_recovery_suggestion(e, aweme_id, request_id)
            if recovery_suggestion:
                self.logger.info(f"[{request_id}] 恢复建议: {recovery_suggestion}")
            raise HTTPException(status_code=403, detail=f"遇到反爬虫保护: {e}")
        except RateLimitError as e:
            error_context = {
                **log_context,
                "error_type": "rate_limit_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] 移动端分享页面请求频率限制: {e}", extra=error_context)
            raise HTTPException(status_code=429, detail=f"请求频率限制: {e}")
        except NetworkError as e:
            error_context = {
                **log_context,
                "error_type": "network_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] 移动端分享页面网络错误: {e}", extra=error_context)
            raise HTTPException(status_code=503, detail=f"网络错误: {e}")
        except Exception as e:
            error_context = {
                **log_context,
                "error_type": "unexpected_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.exception(f"[{request_id}] 移动端分享页面数据获取异常: {e}", extra=error_context)
            raise HTTPException(status_code=500, detail=f"移动端分享页面数据获取异常: {str(e)}")

    async def fetch_pc_video_data(
        self,
        aweme_id: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        save_to_db: bool = True,
    ) -> Dict:
        """
        通过PC端视频页面获取视频数据

        Args:
            aweme_id: 抖音视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、html_response等字段的结果

        Raises:
            HTTPException: 请求失败时抛出
        """
        request_id = f"pc_video_{aweme_id}_{int(time.time())}"
        operation_start_time = time.time()

        # 创建结构化日志上下文
        log_context = {
            "request_id": request_id,
            "aweme_id": aweme_id,
            "operation": "fetch_pc_video_data",
            "use_proxy": use_proxy,
            "timeout": timeout,
            "timestamp": operation_start_time,
        }

        self.logger.info(f"[{request_id}] 开始通过PC端视频页面获取视频数据: {aweme_id}", extra=log_context)

        try:
            # 创建PC端视频页面请求
            pc_request = PCVideoRequest(
                aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers, timeout=timeout
            )

            # 使用HTML客户端获取数据
            async with DouyinHTMLClient(self.config) as client:
                html_response = await client.fetch_pc_video_page(pc_request)

            if not html_response.success:
                error_context = {
                    **log_context,
                    "error_type": "html_request_failed",
                    "error_message": html_response.error_message,
                    "operation_duration": time.time() - operation_start_time,
                }
                self.logger.error(
                    f"[{request_id}] PC端视频页面请求失败: {html_response.error_message}", extra=error_context
                )
                raise HTTPException(status_code=500, detail=f"PC端视频页面请求失败: {html_response.error_message}")

            # HTML内容诊断
            html_diagnostics = self._diagnose_html_content(html_response.html_content, aweme_id, request_id)
            diagnostic_context = {**log_context, **html_diagnostics}

            self.logger.debug(f"[{request_id}] PC端视频页面HTML内容诊断完成", extra=diagnostic_context)

            # 解析HTML内容提取视频数据
            video_data = await self._extract_video_data_from_html(
                html_response.html_content, aweme_id, source="pc_video"
            )

            if not video_data:
                error_context = {
                    **log_context,
                    "error_type": "data_extraction_failed",
                    "html_size": len(html_response.html_content) if html_response.html_content else 0,
                    "operation_duration": time.time() - operation_start_time,
                }
                self.logger.error(f"[{request_id}] 从PC端视频页面HTML中提取视频数据失败", extra=error_context)
                raise HTTPException(status_code=404, detail="从PC端视频页面HTML中提取视频数据失败")



            # 保存到数据库（如果需要）
            if save_to_db:
                save_success = await self._save_video_data_to_db(video_data, source="pc_video")
                if not save_success:
                    self.logger.warning(
                        f"[{request_id}] PC端视频页面数据保存到数据库失败",
                        extra={**log_context, "save_to_db_failed": True},
                    )

            operation_duration = time.time() - operation_start_time
            success_context = {
                **log_context,
                "operation_duration": operation_duration,
                "save_to_db": save_to_db,
                "html_response_info": {
                    "status_code": html_response.status_code,
                    "response_time": html_response.response_time,
                    "retry_count": html_response.retry_count,
                },
            }

            self.logger.info(f"[{request_id}] PC端视频页面数据获取成功", extra=success_context)

            return {
                "success": True,
                "data": video_data,
                "html_response": {
                    "status_code": html_response.status_code,
                    "response_time": html_response.response_time,
                    "retry_count": html_response.retry_count,
                    "request_id": html_response.request_id,
                    "url": html_response.url,
                    "final_url": html_response.final_url,
                },
                "source": "pc_video",
                "request_id": request_id,
            }

        except HTTPException:
            raise
        except AntiCrawlerError as e:
            error_context = {
                **log_context,
                "error_type": "anti_crawler_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] PC端视频页面遇到反爬虫保护: {e}", extra=error_context)
            # 提供恢复建议
            recovery_suggestion = self._get_recovery_suggestion(e, aweme_id, request_id)
            if recovery_suggestion:
                self.logger.info(f"[{request_id}] 恢复建议: {recovery_suggestion}")
            raise HTTPException(status_code=403, detail=f"遇到反爬虫保护: {e}")
        except RateLimitError as e:
            error_context = {
                **log_context,
                "error_type": "rate_limit_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] PC端视频页面请求频率限制: {e}", extra=error_context)
            raise HTTPException(status_code=429, detail=f"请求频率限制: {e}")
        except NetworkError as e:
            error_context = {
                **log_context,
                "error_type": "network_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] PC端视频页面网络错误: {e}", extra=error_context)
            raise HTTPException(status_code=503, detail=f"网络错误: {e}")
        except Exception as e:
            error_context = {
                **log_context,
                "error_type": "unexpected_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.exception(f"[{request_id}] PC端视频页面数据获取异常: {e}", extra=error_context)
            raise HTTPException(status_code=500, detail=f"PC端视频页面数据获取异常: {str(e)}")

    async def fetch_user_profile_data(
        self,
        sec_uid: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
    ) -> Dict:
        """
        通过用户主页获取用户数据

        Args:
            sec_uid: 用户的sec_uid
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间

        Returns:
            Dict: 包含success、data、html_response等字段的结果

        Raises:
            HTTPException: 请求失败时抛出
        """
        request_id = f"user_profile_{sec_uid}_{int(time.time())}"
        operation_start_time = time.time()

        # 创建结构化日志上下文
        log_context = {
            "request_id": request_id,
            "sec_uid": sec_uid,
            "operation": "fetch_user_profile_data",
            "use_proxy": use_proxy,
            "timeout": timeout,
            "timestamp": operation_start_time,
        }

        self.logger.info(f"[{request_id}] 开始通过用户主页获取用户数据: {sec_uid}", extra=log_context)

        try:
            # 创建用户主页请求
            profile_request = UserProfileRequest(
                sec_uid=sec_uid, use_proxy=use_proxy, custom_headers=custom_headers, timeout=timeout
            )

            # 使用HTML客户端获取数据
            async with DouyinHTMLClient(self.config) as client:
                html_response = await client.fetch_user_profile_page(profile_request)

            if not html_response.success:
                error_context = {
                    **log_context,
                    "error_type": "html_request_failed",
                    "error_message": html_response.error_message,
                    "operation_duration": time.time() - operation_start_time,
                }
                self.logger.error(
                    f"[{request_id}] 用户主页请求失败: {html_response.error_message}", extra=error_context
                )
                raise HTTPException(status_code=500, detail=f"用户主页请求失败: {html_response.error_message}")

            # HTML内容诊断
            html_diagnostics = self._diagnose_html_content(html_response.html_content, sec_uid, request_id)
            diagnostic_context = {**log_context, **html_diagnostics}

            self.logger.debug(f"[{request_id}] 用户主页HTML内容诊断完成", extra=diagnostic_context)

            # 解析HTML内容提取用户数据
            user_data = await self._extract_user_data_from_html(html_response.html_content, sec_uid)

            if not user_data:
                error_context = {
                    **log_context,
                    "error_type": "data_extraction_failed",
                    "html_size": len(html_response.html_content) if html_response.html_content else 0,
                    "operation_duration": time.time() - operation_start_time,
                }
                self.logger.error(f"[{request_id}] 从用户主页HTML中提取用户数据失败", extra=error_context)
                raise HTTPException(status_code=404, detail="从用户主页HTML中提取用户数据失败")

            operation_duration = time.time() - operation_start_time
            success_context = {
                **log_context,
                "operation_duration": operation_duration,
                "html_response_info": {
                    "status_code": html_response.status_code,
                    "response_time": html_response.response_time,
                    "retry_count": html_response.retry_count,
                },
            }

            self.logger.info(f"[{request_id}] 用户主页数据获取成功", extra=success_context)

            return {
                "success": True,
                "data": user_data,
                "html_response": {
                    "status_code": html_response.status_code,
                    "response_time": html_response.response_time,
                    "retry_count": html_response.retry_count,
                    "request_id": html_response.request_id,
                    "url": html_response.url,
                    "final_url": html_response.final_url,
                },
                "source": "user_profile",
                "request_id": request_id,
            }

        except HTTPException:
            raise
        except AntiCrawlerError as e:
            error_context = {
                **log_context,
                "error_type": "anti_crawler_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] 用户主页遇到反爬虫保护: {e}", extra=error_context)
            # 提供恢复建议
            recovery_suggestion = self._get_recovery_suggestion(e, sec_uid, request_id)
            if recovery_suggestion:
                self.logger.info(f"[{request_id}] 恢复建议: {recovery_suggestion}")
            raise HTTPException(status_code=403, detail=f"遇到反爬虫保护: {e}")
        except RateLimitError as e:
            error_context = {
                **log_context,
                "error_type": "rate_limit_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] 用户主页请求频率限制: {e}", extra=error_context)
            raise HTTPException(status_code=429, detail=f"请求频率限制: {e}")
        except NetworkError as e:
            error_context = {
                **log_context,
                "error_type": "network_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{request_id}] 用户主页网络错误: {e}", extra=error_context)
            raise HTTPException(status_code=503, detail=f"网络错误: {e}")
        except Exception as e:
            error_context = {
                **log_context,
                "error_type": "unexpected_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.exception(f"[{request_id}] 用户主页数据获取异常: {e}", extra=error_context)
            raise HTTPException(status_code=500, detail=f"用户主页数据获取异常: {str(e)}")


    async def _extract_video_data_from_html(self, html_content: str, aweme_id: str, source: str) -> Optional[Dict]:
        """
        从HTML内容中提取视频数据

        Args:
            html_content: HTML内容
            aweme_id: 视频ID
            source: 数据来源

        Returns:
            Optional[Dict]: 提取的视频数据或None
        """
        operation_start_time = time.time()
        extraction_context = f"aweme_id:{aweme_id}|source:{source}"

        # 创建结构化日志上下文
        log_context = {
            "aweme_id": aweme_id,
            "source": source,
            "operation": "extract_video_data_from_html",
            "html_length": len(html_content) if html_content else 0,
            "timestamp": operation_start_time,
        }

        self.logger.debug(f"[{extraction_context}] 开始从HTML提取视频数据", extra=log_context)

        try:
            # 输入验证
            if not html_content:
                error_msg = "HTML内容为空"
                self.logger.warning(
                    f"[{extraction_context}] 输入验证失败: {error_msg}",
                    extra={**log_context, "validation_error": "empty_html_content"},
                )
                return None

            if not aweme_id:
                error_msg = "aweme_id为空"
                self.logger.warning(
                    f"[{extraction_context}] 输入验证失败: {error_msg}",
                    extra={**log_context, "validation_error": "empty_aweme_id"},
                )
                return None

            # HTML内容诊断
            html_diagnostics = self._diagnose_html_content(html_content, aweme_id, f"extract_{source}")
            diagnostic_context = {**log_context, **html_diagnostics}

            self.logger.debug(f"[{extraction_context}] HTML内容诊断完成", extra=diagnostic_context)



            with DouyinDataExtractor() as extractor:
                # 步骤1: 从HTML中提取router数据
                self.logger.debug(f"[{extraction_context}] 步骤1: 提取router数据")
                router_data = extractor.extract_router_data_from_html(html_content)

                if not router_data:
                    error_msg = "无法从HTML中提取router数据"
                    self.logger.warning(
                        f"[{extraction_context}] router数据提取失败: {error_msg}",
                        extra={**log_context, "extraction_stage": "router_extraction", "found_data": False},
                    )
                    return None

                self.logger.debug(
                    f"[{extraction_context}] router数据提取成功",
                    extra={
                        **log_context,
                        "router_data_type": type(router_data).__name__,
                        "extraction_stage": "router_extraction",
                    },
                )

                # 步骤2: 从router数据中提取视频信息
                self.logger.debug(f"[{extraction_context}] 步骤2: 从router数据提取视频信息")
                video_info = extractor.extract_video_info(router_data)

                if video_info:
                    # 添加来源标识
                    video_info["source_keyword"] = source

                    operation_duration = time.time() - operation_start_time
                    success_context = {
                        **log_context,
                        "extraction_duration": operation_duration,
                        "video_info_keys": list(video_info.keys()) if isinstance(video_info, dict) else "non_dict",
                    }

                    self.logger.info(f"[{extraction_context}] 视频数据提取成功", extra=success_context)

                    return video_info
                else:
                    error_msg = "无法从router数据中提取视频信息"
                    operation_duration = time.time() - operation_start_time
                    self.logger.warning(
                        f"[{extraction_context}] 视频信息提取失败: {error_msg}",
                        extra={
                            **log_context,
                            "extraction_stage": "video_info_extraction",
                            "found_data": False,
                            "operation_duration": operation_duration,
                        },
                    )
                    return None

        except Exception as e:
            operation_duration = time.time() - operation_start_time
            error_context = {
                **log_context,
                "operation_duration": operation_duration,
                "error_type": type(e).__name__,
                "error_detail": str(e),
                "extraction_stage": "general_extraction",
            }

            self.logger.error(f"[{extraction_context}] 从HTML提取视频数据异常: {e}", extra=error_context)
            return None

    async def _extract_jingxuan_data_from_html(
        self, html_content: str, aweme_id: str, request_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        从精选页面HTML中提取视频数据，使用JingxuanDataExtractor（已集成性能优化）

        Args:
            html_content: HTML内容
            aweme_id: 视频ID
            request_id: 请求ID，用于日志追踪

        Returns:
            提取的视频数据字典，如果提取失败则返回None

        Raises:
            JingxuanParsingError: HTML解析失败
            JingxuanDataError: 数据格式错误
            JingxuanConversionError: 数据转换失败
            JingxuanValidationError: 数据验证失败
            JingxuanTimeoutError: 操作超时
            JingxuanExtractionError: 其他提取错误
        """
        extraction_context = f"request_id:{request_id}|aweme_id:{aweme_id}"
        operation_start_time = time.time()

        # 导入性能优化器
        from utils.douyin.performance_optimizer import (
            default_performance_optimizer,
            process_html_optimized,
        )

        # 创建结构化日志上下文
        log_context = {
            "request_id": request_id,
            "aweme_id": aweme_id,
            "operation": "extract_jingxuan_data",
            "html_length": len(html_content) if html_content else 0,
            "timestamp": operation_start_time,
            "performance_optimized": True,
        }

        self.logger.info(f"[{extraction_context}] 开始从精选页面HTML提取视频数据（已启用性能优化）", extra=log_context)

        try:
            # 输入验证和HTML内容诊断
            if not html_content:
                error_msg = "HTML内容为空"
                self.logger.error(
                    f"[{extraction_context}] HTML内容验证失败: {error_msg}",
                    extra={**log_context, "validation_error": "empty_content"},
                )
                raise JingxuanDataError(error_msg, aweme_id=aweme_id, data_type="html_content")

            # HTML内容基本诊断
            html_diagnostics = self._diagnose_html_content(html_content, aweme_id, request_id)
            diagnostic_context = {**log_context, **html_diagnostics}

            self.logger.debug(f"[{extraction_context}] HTML内容诊断完成", extra=diagnostic_context)

            # 使用性能优化的数据提取
            async with default_performance_optimizer.optimized_extraction("extract_jingxuan_data", aweme_id):
                # 创建JingxuanDataExtractor实例
                extractor = JingxuanDataExtractor()

                # HTML内容诊断
                html_diagnosis = self._diagnose_html_content(html_content, aweme_id, request_id)

                # 步骤1: 使用性能优化提取pace_f数据
                self.logger.debug(f"[{extraction_context}] 步骤1: 提取pace_f数据（性能优化）")

                # 预处理HTML（内存优化）
                optimized_html, processing_stats = process_html_optimized(html_content, aweme_id)

                pace_f_data = extractor.extract_pace_f_data(optimized_html, aweme_id)

                if not pace_f_data:
                    error_msg = f"未找到包含aweme_id '{aweme_id}' 的pace_f条目"
                    self.logger.warning(
                        f"[{extraction_context}] pace_f数据提取失败: {error_msg}",
                        extra={**log_context, "extraction_stage": "pace_f_extraction", "found_data": False},
                    )
                    return None

                self.logger.debug(
                    f"[{extraction_context}] pace_f数据提取成功",
                    extra={
                        **log_context,
                        "pace_f_data_length": len(pace_f_data),
                        "extraction_stage": "pace_f_extraction",
                    },
                )

                # 步骤2: URI解码pace_f数据
                self.logger.debug(f"[{extraction_context}] 步骤2: URI解码pace_f数据")
                decoded_data = extractor.decode_uri_component(pace_f_data, aweme_id)

                if not decoded_data:
                    error_msg = "URI解码后数据为空"
                    self.logger.warning(
                        f"[{extraction_context}] URI解码失败: {error_msg}",
                        extra={**log_context, "extraction_stage": "uri_decoding", "decoded_data": False},
                    )
                    return None

                self.logger.debug(
                    f"[{extraction_context}] URI解码成功",
                    extra={**log_context, "decoded_data_length": len(decoded_data), "extraction_stage": "uri_decoding"},
                )

                # 步骤3: 解析JSON数据
                self.logger.debug(f"[{extraction_context}] 步骤3: 解析JSON数据")
                json_data = extractor.parse_json_data(decoded_data, aweme_id)

                if not json_data:
                    error_msg = "JSON解析后数据为空"
                    self.logger.warning(
                        f"[{extraction_context}] JSON解析失败: {error_msg}",
                        extra={**log_context, "extraction_stage": "json_parsing", "parsed_data": False},
                    )
                    return None

                self.logger.debug(
                    f"[{extraction_context}] JSON解析成功",
                    extra={
                        **log_context,
                        "json_data_keys": list(json_data.keys()) if isinstance(json_data, dict) else "non_dict",
                        "extraction_stage": "json_parsing",
                    },
                )

                # 步骤4: 转换为AwemeItem格式
                self.logger.debug(f"[{extraction_context}] 步骤4: 转换为AwemeItem格式")
                mapper = JingxuanDataExtractorMapper()
                aweme_item = mapper.convert_to_aweme_item(json_data)

                if not aweme_item:
                    error_msg = "数据转换为AwemeItem格式失败"
                    self.logger.warning(
                        f"[{extraction_context}] AwemeItem转换失败: {error_msg}",
                        extra={**log_context, "extraction_stage": "aweme_conversion", "converted_data": False},
                    )
                    return None

                operation_duration = time.time() - operation_start_time
                success_context = {
                    **log_context,
                    "extraction_duration": operation_duration,
                    "final_data_keys": list(aweme_item.keys()) if isinstance(aweme_item, dict) else "non_dict",
                    "html_processing_stats": processing_stats,
                }

                # 添加来源标识
                aweme_item["source_keyword"] = "jingxuan"

                self.logger.info(f"[{extraction_context}] 精选页面数据提取成功（性能优化）", extra=success_context)

                return aweme_item

        except JingxuanParsingError as e:
            error_context = {
                **log_context,
                "error_type": "parsing_error",
                "error_code": e.error_code,
                "parsing_stage": getattr(e, "parsing_stage", "unknown"),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.warning(f"[{extraction_context}] HTML解析错误: {e}", extra=error_context)
            # 尝试恢复策略
            recovery_suggestion = self._get_recovery_suggestion(e, aweme_id, request_id)
            if recovery_suggestion:
                self.logger.info(f"[{extraction_context}] 错误恢复建议: {recovery_suggestion}", extra=error_context)
            raise

        except JingxuanDataError as e:
            error_context = {
                **log_context,
                "error_type": "data_error",
                "error_code": e.error_code,
                "data_type": getattr(e, "data_type", "unknown"),
                "validation_field": getattr(e, "validation_field", "unknown"),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{extraction_context}] 数据格式错误: {e}", extra=error_context)
            raise

        except JingxuanConversionError as e:
            error_context = {
                **log_context,
                "error_type": "conversion_error",
                "error_code": e.error_code,
                "conversion_stage": getattr(e, "conversion_stage", "unknown"),
                "missing_fields": getattr(e, "missing_fields", []),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{extraction_context}] 数据转换错误: {e}", extra=error_context)
            raise

        except JingxuanValidationError as e:
            error_context = {
                **log_context,
                "error_type": "validation_error",
                "error_code": e.error_code,
                "validation_type": getattr(e, "validation_type", "unknown"),
                "expected_value": getattr(e, "expected_value", "unknown"),
                "actual_value": getattr(e, "actual_value", "unknown"),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{extraction_context}] 数据验证错误: {e}", extra=error_context)
            raise

        except JingxuanTimeoutError as e:
            error_context = {
                **log_context,
                "error_type": "timeout_error",
                "error_code": e.error_code,
                "timeout_seconds": getattr(e, "timeout_seconds", "unknown"),
                "operation": getattr(e, "operation", "unknown"),
                "retry_count": getattr(e, "retry_count", 0),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{extraction_context}] 操作超时错误: {e}", extra=error_context)
            raise

        except JingxuanExtractionError as e:
            error_context = {
                **log_context,
                "error_type": "extraction_error",
                "error_code": e.error_code,
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{extraction_context}] 精选提取错误: {e}", extra=error_context)
            raise

        except Exception as e:
            error_context = {
                **log_context,
                "error_type": "unexpected_error",
                "error_detail": str(e),
                "operation_duration": time.time() - operation_start_time,
            }
            self.logger.error(f"[{extraction_context}] 未预期的错误: {e}", extra=error_context)
            # 将未知错误包装为JingxuanExtractionError
            raise JingxuanExtractionError(f"精选数据提取时发生未知错误: {e}", aweme_id=aweme_id)

    def _diagnose_html_content(self, html_content: str, aweme_id: str, request_id: str) -> Dict[str, Any]:
        """
        诊断HTML内容的基本特征和质量

        Args:
            html_content: HTML内容
            aweme_id: 视频ID
            request_id: 请求ID

        Returns:
            包含HTML诊断信息的字典
        """
        import re

        diagnostics = {
            "html_size": len(html_content),
            "has_pace_f": "pace_f" in html_content,
            "has_aweme_id": aweme_id in html_content if aweme_id else False,
            "has_router_data": "_ROUTER_DATA" in html_content,
            "has_script_tags": bool(re.search(r"<script[^>]*>", html_content)),
            "script_tag_count": len(re.findall(r"<script[^>]*>", html_content)),
            "pace_f_count": html_content.count("pace_f") if html_content else 0,
            "has_json_structure": bool(re.search(r'\{[^}]*"[^"]*":[^}]*\}', html_content)),
            "encoding_issues": self._detect_encoding_issues(html_content),
        }

        return diagnostics

    def _detect_encoding_issues(self, html_content: str) -> List[str]:
        """检测HTML内容中的编码问题"""
        issues = []

        if not html_content:
            return issues

        # 检测常见的编码问题
        if "�" in html_content:
            issues.append("replacement_character_found")

        if html_content.count("%") > len(html_content) * 0.1:
            issues.append("excessive_url_encoding")

        # 检测不完整的UTF-8序列
        try:
            html_content.encode("utf-8").decode("utf-8")
        except UnicodeError:
            issues.append("utf8_encoding_error")

        return issues



    def _get_recovery_suggestion(self, error: Exception, aweme_id: str, request_id: str) -> Optional[str]:
        """
        根据错误类型提供恢复建议

        Args:
            error: 发生的异常
            aweme_id: 视频ID
            request_id: 请求ID

        Returns:
            恢复建议字符串，如果没有建议则返回None
        """
        error_type = type(error).__name__

        suggestions = {
            "JingxuanParsingError": "建议检查HTML结构是否发生变化，或尝试使用备用解析策略",
            "JingxuanDataError": "建议验证输入数据格式，或检查数据源是否有更新",
            "JingxuanConversionError": "建议检查数据转换逻辑，或使用兼容性更好的转换方法",
            "JingxuanValidationError": "建议放宽验证条件，或更新验证规则以适应新的数据格式",
            "JingxuanTimeoutError": "建议增加超时时间，或使用异步处理方式",
            "JingxuanNetworkError": "建议检查网络连接，或使用重试机制",
            "JingxuanRateLimitError": "建议降低请求频率，或使用代理池分散请求",
        }

        base_suggestion = suggestions.get(error_type)

        if base_suggestion:
            return f"{base_suggestion} (aweme_id: {aweme_id}, request_id: {request_id})"

        return None

    async def _extract_user_data_from_html(self, html_content: str, sec_uid: str) -> Optional[Dict]:
        """
        从HTML内容中提取用户数据

        Args:
            html_content: HTML内容
            sec_uid: 用户sec_uid

        Returns:
            Optional[Dict]: 提取的用户数据或None
        """
        try:
            # 这里可以使用类似的数据提取器来解析用户页面
            # 由于项目中可能没有专门的用户数据提取器，这里先返回基本结构
            self.logger.warning("用户数据提取功能待实现")
            return {"sec_uid": sec_uid, "source": "user_profile", "extract_time": time.time()}

        except Exception as e:
            self.logger.error(f"从HTML提取用户数据异常: {e}")
            return None

    async def _save_video_data_to_db(self, video_data: Dict, source: str) -> bool:
        """
        保存视频数据到数据库

        Args:
            video_data: 视频数据
            source: 数据来源

        Returns:
            bool: 保存是否成功
        """
        operation_start_time = time.time()
        aweme_id = video_data.get("aweme_id", "unknown")
        save_context = f"aweme_id:{aweme_id}|source:{source}"

        # 创建结构化日志上下文
        log_context = {
            "aweme_id": aweme_id,
            "source": source,
            "operation": "save_video_data_to_db",
            "data_keys": list(video_data.keys()) if isinstance(video_data, dict) else "non_dict",
            "data_size": len(str(video_data)) if video_data else 0,
            "timestamp": operation_start_time,
        }

        self.logger.debug(f"[{save_context}] 开始保存视频数据到数据库", extra=log_context)

        try:
            # 输入验证
            if not video_data:
                error_msg = "视频数据为空"
                self.logger.warning(
                    f"[{save_context}] 输入验证失败: {error_msg}",
                    extra={**log_context, "validation_error": "empty_video_data"},
                )
                return False

            if not isinstance(video_data, dict):
                error_msg = f"视频数据格式错误，期望dict，实际: {type(video_data).__name__}"
                self.logger.warning(
                    f"[{save_context}] 输入验证失败: {error_msg}",
                    extra={
                        **log_context,
                        "validation_error": "invalid_data_type",
                        "actual_type": type(video_data).__name__,
                    },
                )
                return False

            if not aweme_id or aweme_id == "unknown":
                error_msg = "aweme_id缺失或无效"
                self.logger.warning(
                    f"[{save_context}] 输入验证失败: {error_msg}",
                    extra={**log_context, "validation_error": "invalid_aweme_id"},
                )
                return False

            # 数据预处理
            processed_data = video_data.copy()
            processed_data["source_keyword"] = source
            processed_data["save_timestamp"] = operation_start_time

            self.logger.debug(f"[{save_context}] 准备保存数据到数据库", extra=log_context)

            # 导入数据库操作函数
            try:
                from models.douyin.models import update_douyin_aweme
            except ImportError as import_error:
                error_context = {
                    **log_context,
                    "error_type": "import_error",
                    "error_detail": str(import_error),
                    "operation_duration": time.time() - operation_start_time,
                }
                self.logger.error(f"[{save_context}] 导入数据库模型失败: {import_error}", extra=error_context)
                return False

            # 创建 DouyinAweme 实例
            aweme_instance = DouyinAweme(**processed_data)

            # 保存到数据库
            self.logger.debug(f"[{save_context}] 执行数据库保存操作")
            success = await update_douyin_aweme(aweme_instance)

            operation_duration = time.time() - operation_start_time

            if success:
                success_context = {
                    **log_context,
                    "operation_duration": operation_duration,
                    "save_success": True,
                }
                self.logger.info(f"[{save_context}] 视频数据保存成功", extra=success_context)
                return True
            else:
                failure_context = {
                    **log_context,
                    "operation_duration": operation_duration,
                    "save_success": False,
                    "failure_reason": "database_operation_failed",
                }
                self.logger.error(f"[{save_context}] 视频数据保存失败: 数据库操作返回False", extra=failure_context)
                return False

        except Exception as e:
            operation_duration = time.time() - operation_start_time
            error_context = {
                **log_context,
                "operation_duration": operation_duration,
                "error_type": type(e).__name__,
                "error_detail": str(e),
                "save_success": False,
            }

            self.logger.exception(f"[{save_context}] 保存视频数据到数据库异常: {e}", extra=error_context)
            return False
